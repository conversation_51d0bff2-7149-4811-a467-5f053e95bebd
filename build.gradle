import com.github.jk1.license.render.CsvReportRenderer

plugins {
    id("idea")
    id("war")
    id("io.spring.dependency-management") version("1.1.4")
    id("com.github.jk1.dependency-license-report") version ("2.4")
}

apply from: rootProject.file("gradle/dependencies.gradle")

allprojects {
    apply plugin: "java"
    apply plugin: "java-library"
    java.sourceCompatibility = JavaVersion.VERSION_17
    java.targetCompatibility = JavaVersion.VERSION_17
}

idea {
    module {
        excludeDirs += file(".gradle")
        [".settings", "classes", "bin", "out", "docs", "dependency-cache", "libs",
         "reports", "resources", "test-results", "tmp"].each {
            excludeDirs += file("$buildDir/$it")
        }
    }
}


dependencies {
    compileOnly(fileTree(dir: 'WebContent/WEB-INF/lib', include: ['*.jar']))

    implementation(project(":orbis-xss-utils"))
    implementation(project(":orbis-common"))

    api libraries.log4j
    api libraries.slf4j

    implementation libraries.spring
    implementation libraries.springsecurity
    implementation libraries.springsecurityweb
    implementation libraries.springsecurityconfig
    implementation libraries.springsecurityacl
    implementation libraries.jackson


    implementation("org.springframework.security:spring-security-oauth2-core:$springSecurityVersion")
    implementation("org.springframework.security:spring-security-oauth2-jose:$springSecurityVersion")
    implementation("org.springframework.security:spring-security-oauth2-resource-server:$springSecurityVersion")
    implementation("org.springframework.security:spring-security-oauth2-authorization-server:1.2.1")

    implementation("org.springframework:spring-test:6.0.13")

    implementation libraries.hibernate
    implementation("org.hibernate.validator:hibernate-validator:8.0.1.Final")

    implementation("org.jsoup:jsoup:1.13.1")
    implementation("commons-lang:commons-lang:2.6")
    implementation("commons-codec:commons-codec:1.8")
    implementation("joda-time:joda-time:1.6.2")
    implementation("com.cronutils:cron-utils:9.1.6") {
        exclude(group: "org.slf4j", module: "slf4j-api")
    }

    implementation("commons-attributes:commons-attributes-api:2.1")
    implementation("commons-beanutils:commons-beanutils:1.7.0")
    implementation("commons-collections:commons-collections:3.2.2")
    implementation("commons-dbcp:commons-dbcp:1.2.1")

    implementation("commons-discovery:commons-discovery:0.2")
    implementation("commons-fileupload:commons-fileupload:1.2.2")
    implementation("org.apache.commons:commons-fileupload2-jakarta:2.0.0-M1")

    implementation("commons-httpclient:commons-httpclient:3.0")
    implementation('commons-io:commons-io:2.14.0')
    implementation("commons-net:commons-net:1.4.1")


    implementation("jdom:jdom:1.0") // uses javax.servlet
    implementation("dom4j:dom4j:1.6.1")
    implementation("odmg:odmg:3.0")

    // data base connection
    implementation("com.zaxxer:HikariCP:5.0.1") {
        exclude(group: "org.slf4j", module: "slf4j-api")
    }
    implementation("com.microsoft.sqlserver:mssql-jdbc:11.2.3.jre17")
    implementation("com.oracle.database.*********************")
    implementation("com.oracle.database.*************************")

    implementation("org.quartz-scheduler:quartz:2.3.2") {
        exclude(group: "org.slf4j", module: "slf4j-api")
    }

    implementation("org.ehcache:ehcache:3.10.8") {
        capabilities {
            requireCapability('org.ehcache:ehcache-jakarta')
        }
        exclude(group: "org.slf4j", module: "slf4j-api")
        exclude(group: "org.glassfish.jaxb", module: "jaxb-runtime")
    }


    providedCompile("org.apache.tomcat:tomcat-servlet-api:10.1.13")
    providedCompile("org.apache.tomcat:tomcat-jasper:10.1.13")
    providedCompile("org.apache.tomcat:tomcat-jsp-api:10.1.13")
    providedCompile("org.apache.tomcat:tomcat-coyote:10.1.13")
    providedCompile("org.apache.tomcat:tomcat-util:10.1.13")
    providedCompile("org.apache.tomcat:tomcat-util-scan:10.1.13")
    providedCompile("org.apache.tomcat:tomcat-websocket:10.1.13")

    implementation("jakarta.el:jakarta.el-api:5.0.1")

    implementation("org.glassfish.web:jakarta.servlet.jsp.jstl:3.0.0")
    implementation("jakarta.mail:jakarta.mail-api:2.1.2")
    implementation("org.eclipse.angus:jakarta.mail:2.0.3")
    implementation("jakarta.servlet.jsp.jstl:jakarta.servlet.jsp.jstl-api:3.0.0")
    implementation("jakarta.servlet:jakarta.servlet-api:6.0.0")
    implementation("jakarta.annotation:jakarta.annotation-api:2.1.1")
    implementation("jakarta.validation:jakarta.validation-api:3.0.2")
    implementation("jakarta.transaction:jakarta.transaction-api:2.0.1")

    compileOnly("org.projectlombok:lombok:1.18.26")
    annotationProcessor("org.projectlombok:lombok:1.18.26")

    implementation("xerces:xercesImpl:2.10.0")
    implementation("org.apache.xmlbeans:xmlbeans:2.6.0")
    implementation("aspectj:aspectjrt:1.2")
    implementation("org.glassfish.main.javaee-api:javax.jws:3.1.2")
    implementation('org.aspectj:aspectjweaver:1.9.21')


    implementation("com.tokbox:opentok-server-sdk:4.3.1")
    implementation("org.asynchttpclient:async-http-client:2.7.0")
    implementation("org.asynchttpclient:async-http-client-netty-utils:2.7.0")
    implementation("org.apache.axis:axis:1.4")

    implementation("org.jasig.cas.client:cas-client-core:3.4.1") // TODO uses javax.servlet
    implementation("concurrent:concurrent:1.3.3")
    implementation("org.owasp.esapi:esapi:2.0GA") // TODO uses javax.servlet
    implementation("org.atteo:evo-inflector:1.2.2")
    implementation("org.apache.pdfbox:fontbox:1.8.12")
    implementation("de.ruedigermoeller:fst:2.57")
    implementation("org.codehaus.groovy:groovy-all:2.1.1")
    implementation("com.itextpdf:itextpdf:5.5.13")

    implementation("javax.xml.bind:jaxb-api:2.3.0")
    implementation("com.sun.xml.bind:jaxb-core:2.3.0")
    implementation("com.sun.xml.bind:jaxb-impl:2.3.0")
    implementation("com.sun.xml.bind:jaxb-jxc:2.3.0")
    implementation("com.sun.xml.bind:jaxb-xjc:2.3.0")
    implementation("javax.xml:jaxrpc-api:1.1")
//    implementation("javax.xml.ws:jaxws-api:2.1-1")
    implementation("org.apache.pdfbox:jempbox:1.8.12")
    implementation("org.apache.pdfbox:pdfbox:1.8.12")
    implementation("org.jfree:jfreechart:1.0.19")
    implementation("org.jodd:jodd-bean:5.0.10")
    implementation("org.jodd:jodd-core:5.0.10")
    implementation("org.bitbucket.b_c:jose4j:0.5.1")
    implementation('io.bit3:jsass:5.10.5')
    implementation("org.openoffice:juh:3.0.1")
    implementation("junit:junit:3.8.1")
    implementation("org.openoffice:jurt:3.0.1")
    implementation("net.sourceforge.jexcelapi:jxl:2.6.10")
    implementation("io.netty:netty-all:4.1.33.Final")
    implementation("com.typesafe.netty:netty-reactive-streams:2.0.0")
    implementation("org.objenesis:objenesis:2.5.1")
    implementation("org.opensaml:opensaml:2.6.4")
    implementation("org.opensaml:openws:1.5.4") // TODO uses javax.servlet
    implementation("org.apache.poi:poi:3.10.1")
    implementation("org.apache.poi:poi-ooxml:3.10.1")
    implementation("org.apache.poi:poi-ooxml-schemas:3.10.1")
    implementation("org.reactivestreams:reactive-streams:1.0.0")
    implementation("io.projectreactor:reactor-core:3.2.6.RELEASE")
    implementation("org.openoffice:ridl:3.0.1")
    implementation("io.reactivex.rxjava2:rxjava:2.2.7")
    implementation("org.scribe:scribe:1.3.0")
    implementation("xalan:serializer:2.7.1")
    implementation('org.yaml:snakeyaml:2.0')
    implementation("net.coobird:thumbnailator:0.4.5")
    implementation("org.openoffice:unoil:3.0.1")
    implementation("wsdl4j:wsdl4j:1.4")
    implementation("xalan:xalan:2.7.1")
    implementation("xml-resolver:xml-resolver:1.2")
    implementation("org.opensaml:xmltooling:1.4.4")
    implementation("org.apache.pdfbox:xmpbox:1.8.12")
    implementation("org.apache.santuario:xmlsec:1.4.4")
    implementation('org.redisson:redisson:3.20.0')
    testImplementation("com.h2database:h2:1.4.192")

    implementation('com.cksource:ckfinder3:4.0.0')

    implementation("net.sf.jasperreports:jasperreports:6.21.2")
    implementation("org.apache.commons:commons-collections4:4.4")
    implementation("com.github.librepdf:openpdf:2.0.1")

    implementation 'io.jsonwebtoken:jjwt-api:0.12.6'
    runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.12.6'
    runtimeOnly 'io.jsonwebtoken:jjwt-jackson:0.12.6'

    implementation("com.warrenstrange:googleauth:1.5.0")
    implementation("com.sendgrid:sendgrid-java:4.10.3")
}

sourceSets {
    main {
        java {
            srcDirs = ["src/java", "WebContent/site/private/src/java"]
        }
        resources {
            srcDirs = ["src/java", "src/main/resources", "WebContent/site/private/src/java"]
            include("**/*.orm.xml", "**/*.dtd", "**/*.properties", "**/*.xml", "META-INF/**/*.*", "templates/**/*.*")
        }
    }
}


compileJava {
    options.encoding = "windows-1252"
    options.compilerArgs = ["-nowarn"]
}

jar {
    duplicatesStrategy = DuplicatesStrategy.INCLUDE
}


war {
    zip64 = true
    webAppDirectory.set(file("WebContent"))
    archiveFileName.set("Outcome.war")
}

licenseReport {
    renderers = [new CsvReportRenderer()]
}

tasks.register("explodedWar", Sync) {
    duplicatesStrategy = DuplicatesStrategy.INCLUDE
    into "${buildDir}/libs/exploded"
    with war
}

configure(explodedWar) {
    group = "build"
    description = "Build the folder structure instead of a single war file."
}

tasks.register("buildSrJsp", Sync) {
    from "${projectDir}/WebContent/WEB-INF/spiralRobot"
    into "${buildDir}/libs/exploded/WEB-INF/spiralRobot"
}

configure(buildSrJsp) {
    group = "build"
    description = "Copies over only the spiral robot JSPs. This task is to ease editing JSPs outside of any fully featured IDE. Usually used with the --continuous flag."
}

tasks.register("buildJsp", Sync) {
    from "${projectDir}/WebContent/WEB-INF/jsp"
    into "${buildDir}/libs/exploded/WEB-INF/jsp"
}

configure(buildJsp) {
    group = "build"
    description = "Copies over only the non spiral robot JSPs. This task is to ease editing JSPs outside of any fully featured IDE. Usually used with the --continuous flag."
}

tasks.register("buildFrontEndAssets", Sync) {
    from "${projectDir}/WebContent/core"
    into "${buildDir}/libs/exploded/WEB-INF/jsp"
}

configure(buildFrontEndAssets) {
    group = "build"
    description = "Copies over only the core folder that contains all the css, scss and js files. This task is to ease editing JSPs outside of any fully featured IDE. Usually used with the --continuous flag."
}