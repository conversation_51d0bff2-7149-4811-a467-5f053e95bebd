<%@ include file="/WEB-INF/jsp/include.jsp"%>

<c:if test="${!o:canViewDashboardItem('sc1', currentUser, siteElement.contentItem)}">
<script type="text/javascript">
	orbisApp.buildForm({
		action : '<o:encrypt action="displayStudentMyAccount" />'
	}).submit();
</script>
</c:if>

<c:if test="${not empty successMessage}">
<orbis:successMessage>
	${successMessage}
</orbis:successMessage>
</c:if>
<c:if test="${not empty errorMessage}">
<orbis:errorMessage>
	${errorMessage}
</orbis:errorMessage>
</c:if>
<c:if test="${not empty createPackageI18nLabels }">
<c:forEach var="err" items="${createPackageI18nLabels}">
	<orbis:errorMessage>
		<orbis:message code="${err}" />
	</orbis:errorMessage>
</c:forEach>
</c:if>
<c:if test="${not empty validationData.pageErrors}">
	<orbis:errorMessage timeOpen="true">
		<ul>
			<c:forEach var="error" items="${validationData.pageErrors}">
				<li>
					${error.translation}
				</li>
			</c:forEach>
		</ul>
	</orbis:errorMessage>
</c:if>

<c:if test="${o:canViewDashboardItem('sc1', currentUser, siteElement.contentItem)}">
   <orbis:addComponent component="jqueryValidate" version="1.11.1" />
   
<style>
	.list--plain {
	    margin: 0;
	    padding: 0;
	    list-style-type: none;
	}
	
	body.is--article {
		background: white;
		color: rgba(0, 0, 0, 0.75);
	}
	.blog-cards.is--catalogue {
		background: none;
		border-radius: 0px;
	}
	.blog-cards.is--catalogue .blog-cards__list {
		flex-wrap: wrap;
		justify-content: space-between;
	}
	.blog-cards.is--catalogue .blog-cards__item {
		flex-wrap: wrap;
		justify-content: space-between;
		margin: 16px auto;
		box-shadow: none;
		border: 4px solid #f1f1f1;
	}
	.dashboard-header.is--blog-cards::after {
		z-index: 0;
		background-image: radial-gradient(50% 150%, rgba(67, 67, 67, 0.15) 50%, rgba(48, 48, 48, 0.95) 100%) !important;
	}
	.blog-cards {
		display: flex;
		justify-content: flex-start;
		overflow-x: auto;
		padding: 16px;
		background: #e1e1e1;
		border-radius: 2px;
	}
	.blog-cards__list {
	  	display: flex;
	}
	.blog-cards__item {
	  	width: 228px;
	  	max-width: 228px;
	  	height: 243px;
	  	max-height: 243px;
	  	background: white;
	  	box-shadow: 0 16px 32px -16px rgba(0, 0, 0, 0.5);
	  	margin-right: 24px;
	}
	.blog-cards__item-banner {
	  	width: 100%;
	  	height: 124px;
	  	background: #8e8e8e;
	  	position: relative;
	  	overflow: hidden;
	}
	.blog-cards__item-banner::after {
	  	content: "";
	  	display: block;
	  	top: 0;
	  	right: 0;
	  	bottom: 0;
	  	left: 0;
	  	position: absolute;
	  	background: rgba(0, 0, 0, 0.33);
	}
	.blog-cards__item-banner__img {
		width: 100%;
	}
	.blog-cards__item-content {
	  	padding: 8px;
	  	display: flex;
	  	flex-direction: column;
	  	align-items: flex-start;
	  	justify-content: space-between;
	}
	.blog-cards__item-content * {
	  	margin-bottom: 4px;
	  	font-size: 12px;
	}
	.blog-cards__item-filters {
	  	position: absolute;
	  	bottom: 8px;
	  	left: 8px;
	  	right: 8px;
	  	z-index: 10;
	}
	.blog-cards__item-filters__item-btn {
	  	margin-right: 4px;
	}
	.blog-cards__item {
	  	min-width: 238px;
	}
	.blog-cards__item-content > * {
	  	max-width: 100%;
	}
	.blog-article {
	  	background: white;
	  	padding: 24px 16px;
	  	font-size: 20px;
	  	line-height: 1.66;
	  	transform: translatey(128px);
	  	opacity: 0;
	  	transition: all 600ms;
	}
	.blog-article.is--scroll {
	  	transform: translatey(-112px);
	  	opacity: 1;
	  	transition: all 2000ms;
	}
	.blog-article__hero {
	  	height: calc(100vh - 72px);
	  	opacity: 1;
	  	transition: all 400ms ease-out;
	  	justify-content: center;
	  	-webkit-background-size: cover !important;
	  	-moz-background-size: cover !important;
	  	-o-background-size: cover !important;
	  	background-size: cover !important;
	}
	.blog-article__hero::after {
	  	content: "";
	 	display: block;
	  	position: absolute;
	  	top: 0;
	  	right: 0;
	  	bottom: 0;
	  	left: 0;
	  	background-image: radial-gradient(50% 150%, rgba(67, 67, 67, 0.33) 50%, rgba(48, 48, 48, 0.88) 100%);
	  	z-index: 1;
	}
	.blog-article__hero.is--scroll {
	  	opacity: 0;
	  	transition: all 1000ms ease-out;
	}
	.blog-article__hero__title {
	  	z-index: 2;
	  	transform: scale(1);
	  	opacity: 1;
	  	max-width: 900px;
	  	transition: all 400ms ease;
	}
	.blog-article__hero__title.is--scroll {
	  	transform: scale(1.5);
	  	opacity: 0;
	  	transition: all 1000ms ease;
	}
	.blog-article__title-group {
	  	max-width: 700px;
	  	margin: 0 auto;
	}
	.blog-article__img-group {
	  	max-width: 800px;
	  	margin: 0 auto;
	}
	.blog-article__body-group {
	  	max-width: 700px;
	  	margin: 0 auto;
	}
	.blog-article__title {
	  	font-size: 24px;
	}
	@media screen and (min-width: 768px) {
		.blog-article__title {
		    font-size: 48px;
		}
	}
	.blog-article__title__logo {
	  	max-height: 48px;
	}
	.blog-article p {
	  	margin-bottom: 40px;
	}
	
	.branded-blog-card-img__container {
	    position: absolute;
	    left: 0px;
	    bottom: 0px;
	    right: 0;
	    background: rgba(255,255,255,0.66);
	    padding: 4px;
	    display: flex;
	    justify-content: flex-start;
	    align-items: center;
	}
	
	.schedule-item {
	   	background: #ffffff;
	   	display: flex;
	   	height: 80px;
	}
	
	.schedule-item__left {
		display: flex;
	    flex-direction: column;
	    justify-content: space-between;
	    align-items: center;
	    background: #a3a3a3;
	    color: #ffffff;
	    flex-basis: 80px;
	    min-width: 80px;
	    max-width: 80px;
	    width: 80px;
	}
	
	.schedule-item__left .date {
	    display: flex;
	    flex-direction: column;
	    justify-content: center;
	    height: 60px;
	}
	
	.schedule-item__left .date .day {
	    font-size: 32px;
	    font-weight: bold;
	    flex-basis: 32px;
	    line-height: 32px;
	    text-align: center;
	}
	
	.schedule-item__left .date .month {
	    font-size: 12px;
	    font-weight: bold;
	    text-transform: uppercase;
	    flex-basis: 12px;
	    line-height: 12px;
	    text-align: center;
	}
	
	.schedule-item__left .time {
	    font-size: 12px;
	    font-weight: bold;
	    background: rgba(0, 0, 0, 0.33);
	    width: 100%;
	    text-align: center;
	    height: 20px;
	    line-height: 20px;
	}
	
	.schedule-item__right {
	    padding: 12px 8px 8px 8px;
	    display: flex;
	    flex-direction: column;
	    justify-content: center;
	}
	
	.schedule-item__title {
	    margin: 0;
	    margin: 0;
	}
	
	.schedule-item__location {
	    font-size: 11px;
	}
	
	.schedule-item__calendar {
	    font-size: 11px;
	}
	
	.schedule-item__time-status {
	    font-size: 11px;
	}
	
	@media screen and (max-width: 500px) {
	   .modal .schedule-item {
	       height: auto !important;
	   }
	}
	
	.schedule-item {
    width: 100%;
	}

	.schedule-item__right {
	    max-width: calc(95% - 80px);
	}
	
	.schedule-item__title {
	    overflow: hidden;
	    white-space: nowrap;
	    text-overflow: ellipsis;
	}
	
	.schedule-item__location, .schedule-item__time-status {
	    font-size: 1.88vw;
	}
	
	.schedule-item__left.color--bg--blue {
	    background: #3874b1;
	}
	
	@media screen and (min-width: 769px) {
	    .schedule-item {
	        min-width: 300px;
	    }
	
	    .schedule-item__right {
	        width: inherit;
	    }
	
	    .schedule-item__location, .schedule-item__time-status {
	        font-size: 13px;
	    }
	}
	
	@media screen and (max-width: 600px) {
	    .schedule-item__location, .schedule-item__time-status {
	        font-size: 11px;
	    }
	}
</style>

<%@ include file="dashboard_studentTitle.jsp"%>
<%@ include file="dashboard_partnerLinks.jsp"%>
<div class="orbisTabContainer">
	<%@ include file="dashboard_nav.jsp"%>
	<div class="tab-content">
		<div class="row-fluid">
			<div class="span12">
				<%@ include file="dashboard_studentNav.jsp"%>
			</div>
		</div>
		<%--
		<c:if
			test="${not empty siteElement.contentItem.sw7L || not empty siteElement.contentItem.sw7L2 }">
			<div class="row-fluid">
				<div class="span12">
					<div class="widget">
						<div class="widget-content">
							<p>${o:getDashboardItemLabel('sw7', currentUser, siteElement.contentItem, orbisLocale)}</p>
						</div>
					</div>
				</div>
			</div>
		</c:if> --%>
		<c:if test="${o:canViewDashboardItem('sc10', currentUser, siteElement.contentItem) && siteElement.contentItem.sc10d == 0}">
			<%@ include file="dashboard_homeMessages.jsp"%>
		</c:if>
		<div class="row-fluid">
			<div class="span6">
				<c:if test="${o:canViewDashboardItem('sc10', currentUser, siteElement.contentItem) && siteElement.contentItem.sc10d == 1}">
					<%@ include file="dashboard_homeMessages.jsp"%>
				</c:if>
				<c:if test="${o:canViewDashboardItem('sw11', currentUser, siteElement.contentItem)}">
					<%@ include file="dashboard_myInterviewsToBook.jsp"%>
				</c:if>
				<c:if test="${o:canViewDashboardItem('sw22', currentUser, siteElement.contentItem) }">
					<script type="text/javascript">
						$(document).ready(function(){
							orbisApp.getSubscribers("competenciesLoaded").push({
								elements: $(".js--toggle-competency-breakdown-button"),
								callback: function(data){
									$(this).toggle(data.hasCompetencies);
								}
							});
						});
					</script>
					
					<div class="panel panel-default">
						<div class="panel-heading" style="display: flex; justify-content: space-between; align-items: center;">
							<strong>${o:getDashboardItemLabel('sw22', currentUser, siteElement.contentItem, orbisLocale)}</strong>
							<c:if test="${ecModule.competencyIntensitiesEnabled}">
								<o:nav anchor="true" anchorClass="js--toggle-competency-breakdown-button hide" action="displayUserRecordCompetencyBreakdown" userId="${currentUser.id}" comingFrom="dashboard">
									<orbis:message code="i18n.ec_userRecordRecords.Seehowthes1770877987619559" />
								</o:nav>
							</c:if>
						</div>
						<div class="panel-body">
							<ui:ajax action="ajaxLoadStudentCompetenciesPanel" />
						</div>
					</div>
				</c:if>

				<c:if test="${o:canViewDashboardItem('sw3', currentUser, siteElement.contentItem)}">

					<div class="panel panel-default">
						<div class="panel-heading">
							<strong>${o:getDashboardItemLabel('sw3', currentUser, siteElement.contentItem, orbisLocale)}</strong>
						</div>
						<div class="panel-body">
							
							<table class="table stat-table">
								<tbody>
									<tr>
										<td></td>
										<td><orbis:message code="i18n.dashboard_studentHome.NewMessages" /></td>
										<td style="width: 15%;" class="value">
											<o:nav anchor="true" action="displayMessages" newMessages="true">
												<!--STU-0024-TST-->${messageCount}<!--STU-0024-TST-->
											</o:nav>
										</td>
									</tr>
								</tbody>
							</table>
							
						</div>
					</div>
				</c:if>
				<c:if test="${o:canViewDashboardItem('sw2', currentUser, siteElement.contentItem)}">
					<div class="panel panel-default">
						<div class="panel-heading">
							<strong>${o:getDashboardItemLabel('sw2', currentUser, siteElement.contentItem, orbisLocale)}</strong>
							<p class="table-overflow-x__message"><orbis:message code="i18n.dashboard_studentHome.Swipeleftr7510475058863132" /></p>
						</div>
						<div class="panel-body">
							<orbis:ajax action="ajaxLoadStudentUpcomingSchedulePanel" />
						</div>
					</div>
				</c:if>
				
				<c:if test="${siteCode != 'waterloo' }">
					<c:if test="${o:canViewDashboardItem('st11', currentUser, siteElement.contentItem) && o:getConfig('USER_SEARCH_SHOW_SKILLS_PROFILE_TAB') == '1' }">
						<div class="panel panel-default">
							<div class="panel-heading">
								<orbis:message code="i18n.dashboard_studentHome.strongMySk771795776665716" />
							</div>
							<div class="panel-body">
								<div class="row-fluid">
									<div class="span12">
										<%@ include file="/WEB-INF/jsp/ccrm/ccrm_skillsProfileCountWidget.jsp"%>
									</div>
								</div>
							</div>
						</div>
					</c:if>
				</c:if>
				<c:if test="${o:canViewDashboardItem('sw19', currentUser, siteElement.contentItem) && (not empty ecModuleSE || not empty ecViewSE)}">
					<div class="panel panel-default">
						<div class="panel-heading">
							<strong>${o:getDashboardItemLabel('sw19', currentUser, siteElement.contentItem, orbisLocale)}</strong>
						</div>
						<div class="panel-body">
							<c:if test="${not empty ecModuleSE}">
								<o:nav anchor="true" anchorClass="btn btn-primary btn-small" action="displayHome" siteElement="${ecModuleSE}">
									View My Experiential Record
								</o:nav>
							</c:if>
							<c:if test="${not empty ecViewSE}">
								<o:nav anchor="true" anchorClass="btn btn-primary btn-small" action="displayHome" siteElement="${ecViewSE}">
									Search ${ecViewSE.contentItem.name}
								</o:nav>
							</c:if>
						</div>
					</div>
				</c:if>
				<c:if test="${o:canViewDashboardItem('sw12', currentUser, siteElement.contentItem)}">
					<div class="panel panel-default" id="defaultDocuments">
						<div class="panel-heading">
							<strong>${o:getDashboardItemLabel('sw12', currentUser, siteElement.contentItem, orbisLocale)}</strong>
						</div>
						<div class="panel-body">
							<orbis:ajax action="ajaxLoadDefaultDocsPanel" />
						</div>
					</div>
				</c:if>
				
				<%@ include file="dashboard_studentHome_savedOpportunitiesWidget.jsp"%>
			</div>
			<div class="span6">
				<%@ include file="dashboard_onlineScheduleWidget.jsp"%>
				<c:if test="${not empty basicCuratedContent}">
					<div class="panel panel-default" id="featuredPostings">
						<div class="panel-heading"><strong><orbis:message code="i18n.dashboard_studentHome.FutureSkil8023363716420021" /></strong></div>
						<div class="panel-body">

							<div class="blog-cards">
								<ul class="blog-cards__list list--plain">
									<c:forEach var="cc" items="${basicCuratedContent}">
										<li class="blog-cards__item">
											<div class="blog-cards__item-banner">
												<img class="blog-cards__item-banner__img" src="${cc.mediaType == 'IMAGE' ? cc.image.thumbnailFile : cc.video.thumbnail}" alt="Title of Image">
												<div class="branded-blog-card-img__container">
													<img src="https://s3-us-west-2.amazonaws.com/s.cdpn.io/t-522/fsc-logo.svg" alt="Logo for Future Skills Centre" class="branded-blog-card-img" />
												</div>
											</div>
											<div class="blog-cards__item-content">
												<h4>${cc.title.substring(0, (cc.title.length() < 30 ? cc.title.length() : 30))}${cc.title.length() < 30 ? "" : "..."}</h4>

												<c:if test="${cc.companyName != null}">
													<h5>${cc.companyName}</h5>
												</c:if>

												<span class="font--italic text--small"> <fmt:formatDate pattern="MMM dd, yyyy hh:mm a" value="${cc.sentAt}" /></span>
												<ui:button onclick="$('#occc_${cc.id}').modal('toggle');" type="info" classes="margin--t--xs">Read More</ui:button>
											</div>

											<div id="occc_${cc.id}" class="modal hide" role="dialog">
												<div class="modal-header">
													<button type="button" class="close" data-dismiss="modal">×</button>
													<h3 id="">${cc.title}</h3>
												</div>
												<div class="modal-body">
													<c:if test="${cc.website != null}">
														<div class="margin--b--m">
															<a href="${cc.website}" target="_blank">Visit Website</a>
														</div>
													</c:if>
													<div>${cc.body}</div>
													<c:if test="${cc.website != null}">
														<div>
															<h5>Learn More</h5>
															<a href="${cc.learnMore }" target="_blank">${cc.learnMore }</a>
														</div>
													</c:if>
												</div>
												<div class="modal-footer">
													<button class="btn btn-small" data-dismiss="modal"
														aria-hidden="true">
														<orbis:message code="i18n.common.close" />
													</button>
												</div>
											</div>
										</li>
									</c:forEach>
								</ul>
							</div>
						</div>
					</div>
				</c:if>

				<c:if test="${not empty eventCuratedContent}">
					<div class="panel panel-default">
						<div class="panel-heading"><strong><orbis:message code="i18n.dashboard_studentHome.Events2582940497816944" /></strong></div>
						<div class="panel-body">
							<c:forEach var="cc" items="${eventCuratedContent}">
								<div class="schedule-item margin--b--m">
									<div class="schedule-item__left color--bg--blue">
										<div class="date">
											<span class="day"><fmt:formatDate pattern="d" value="${cc.event.start}" /></span>
											<span class="month"><fmt:formatDate pattern="MMM" value="${cc.event.start}" /></span>
										</div>
										<div class="time">
											<span class="schedule-item__left--time-span"> <fmt:formatDate pattern="hh:mm a" value="${cc.event.start}" /></span>
										</div>
									</div>
									<div class="schedule-item__right">
										<h5 class="schedule-item__title">${cc.title}</h5>
										<span class="schedule-item__location">${cc.event.location.name} ${cc.event.location.region}, ${cc.event.location.country}</span>
										<span class="schedule-item__time-status">${cc.event.venue}</span>
									</div>
								</div>
							</c:forEach>
						</div>
					</div>
				</c:if>
		
				<c:if test="${o:canViewDashboardItem('sc10', currentUser, siteElement.contentItem) && siteElement.contentItem.sc10d == 2}">
					<%@ include file="dashboard_homeMessages.jsp"%>
				</c:if>
				<c:if test="${o:canViewDashboardItem('sw18', currentUser, siteElement.contentItem) && serviceTeamEngaged}">
					<c:set var="entityName" value="userId" />
					<c:set var="entityId" value="${currentUser.id}" />
					<c:set var="entityType" value="USER" />
					<%@ include file="/WEB-INF/jsp/interaction/interaction_serviceTeamOverview.jsp"%>
				</c:if>
				<c:if test="${o:canViewDashboardItem('sw9', currentUser, siteElement.contentItem)}">
					<div class="panel panel-default" id="featuredPostings">
						<div class="panel-heading">
							<strong>${o:getDashboardItemLabel('sw9', currentUser, siteElement.contentItem, orbisLocale)}</strong>
							<div id="carouselIndicatorPlaceholder"></div>
						</div>
						<div class="panel-body">
							<orbis:ajax action="ajaxLoadStudentFeaturedJobsPanel" />
						</div>
					</div>
				</c:if>
				<c:if test="${o:canViewDashboardItem('sw10', currentUser, siteElement.contentItem)}">
					<div class="panel panel-default" id="commonForms">
						<div class="panel-heading">
							<strong>${o:getDashboardItemLabel('sw10', currentUser, siteElement.contentItem, orbisLocale)}</strong>
						</div>
						<div class="panel-body">
							<div class="row-fluid">
								<orbis:ajax action="ajaxLoadStudentFormsPanel" />
							</div>
						</div>
					</div>
				</c:if>
				
				<c:if test="${o:canViewDashboardItem('sw14', currentUser, siteElement.contentItem) && not empty currentUser.assignedTypes[siteElement.contentItem.sw14p.name]}">
					<c:forEach var="coopModule" items="${coopModules}">
						<div class="panel panel-default">
							<div class="panel-heading">
								<strong>${isL1 ? coopModule.name : coopModule.name2} - ${o:getDashboardItemLabel('sw14', currentUser, siteElement.contentItem, orbisLocale)}</strong>
							</div>
							<div class="panel-body">
								<orbis:ajax action="ajaxLoadStudentCoopPanel" additionalParams="{coopModuleId : ${coopModule.id}}" />
							</div>
						</div>
					</c:forEach>
				</c:if>
				<c:if test="${o:canViewDashboardItem('sw23', currentUser, siteElement.contentItem) && o:canViewDashboardItem('st16', currentUser, siteElement.contentItem)}">
					<div class="panel panel-default">
						<div class="panel-heading">
							<strong>${o:getDashboardItemLabel('sw23', currentUser, siteElement.contentItem, orbisLocale)}</strong>
						</div>
						<div class="panel-body">
							<o:nav anchor="true" action="displayInMemoryOccJobs">
								<span class="badge badge-inverse">
										${occJobsCount}
								</span>
							</o:nav>
						</div>
					</div>
				</c:if>
				<c:if test="${o:canViewDashboardItem('sw17', currentUser, siteElement.contentItem)}">
					<c:forEach var="npModule" items="${npModules}">
						<div class="panel panel-default">
							<div class="panel-heading">
								<strong>${isL1 ? npModule.name : npModule.name2} - ${o:getDashboardItemLabel('sw17', currentUser, siteElement.contentItem, orbisLocale)}</strong>
							</div>
							<div class="panel-body">
								<orbis:ajax action="ajaxLoadPostingModuleStudentStats" additionalParams="{npModuleId : ${npModule.id}}" />
							</div>
						</div>
					</c:forEach>
				</c:if>
				<c:if test="${o:canViewDashboardItem('sw5', currentUser, siteElement.contentItem)}">

					<div class="panel panel-default">
						<div class="panel-heading">
							<strong>${o:getDashboardItemLabel('sw5', currentUser, siteElement.contentItem, orbisLocale)}</strong>
						</div>
						<div class="panel-body">
							<orbis:ajax action="ajaxLoadStudentUpcomingEventsPanel" />
						</div>
					</div>
				</c:if>
			</div>
		</div>
	</div>
</div>

<c:if test="${not empty globalEventModules}">
	<div id="registerEventDiv" class="modal hide" tabindex="-1"
		role="dialog" aria-labelledby="stuRegisterEventDivTitle" aria-hidden="true">
		<div class="modal-header">
			<button type="button" class="close" data-dismiss="modal"
				aria-hidden="true">×</button>
			<h3 id="stuRegisterEventDivTitle">
				<orbis:message code="i18n.dashboard_studentHome.RegisterforanEvent" />
			</h3>
		</div>
		<div class="modal-body">
			<div class="row-fluid">
				<orbis:message code="i18n.dashboard_studentHome.Selectacalendarto" />
				: <br> <br>
				<ul class="nav nav-tabs nav-stacked">
					<c:forEach var="globalEventModule" items="${globalEventModules}">
						<li><a href="${globalEventModule.value.fullPath}.htm">${globalEventModule.key}</a>
						</li>
					</c:forEach>
				</ul>
			</div>
		</div>
		<div class="modal-footer">
			<button class="btn" data-dismiss="modal" aria-hidden="true">
				<orbis:message code="i18n.dashboard_studentHome.Close" />
			</button>
		</div>
	</div>
</c:if>
</c:if>
